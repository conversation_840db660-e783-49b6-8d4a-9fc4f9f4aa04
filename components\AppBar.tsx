"use client";

import React, { useState } from "react";

const AppBar = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement search functionality
    console.log("Searching for:", searchQuery);
  };

  return (
    <div className="navbar bg-base-100 shadow-lg sticky top-0 z-50">
      {/* Mobile menu button */}
      <div className="navbar-start">
        <div className="dropdown">
          <div tabIndex={0} role="button" className="btn btn-ghost lg:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h8m-8 6h16"
              />
            </svg>
          </div>
          <ul
            tabIndex={0}
            className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
          >
            <li>
              <a href="/">🏠 Trang chủ</a>
            </li>
            <li>
              <a href="/movies">🎬 Phim lẻ</a>
            </li>
            <li>
              <a href="/series">📺 Phim bộ</a>
            </li>
            <li>
              <details>
                <summary>🎭 Thể loại</summary>
                <ul className="p-2">
                  <li>
                    <a href="/genre/action">Hành động</a>
                  </li>
                  <li>
                    <a href="/genre/comedy">Hài kịch</a>
                  </li>
                  <li>
                    <a href="/genre/drama">Chính kịch</a>
                  </li>
                  <li>
                    <a href="/genre/horror">Kinh dị</a>
                  </li>
                  <li>
                    <a href="/genre/romance">Lãng mạn</a>
                  </li>
                  <li>
                    <a href="/genre/sci-fi">Khoa học viễn tưởng</a>
                  </li>
                </ul>
              </details>
            </li>
          </ul>
        </div>

        {/* Logo/Brand */}
        <a href="/" className="btn btn-ghost text-xl font-bold">
          <span className="text-2xl">🎬</span>
          <span className="hidden sm:inline text-primary">AZ Movie</span>
        </a>
      </div>

      {/* Desktop menu */}
      <div className="navbar-center hidden lg:flex">
        <ul className="menu menu-horizontal px-1">
          <li>
            <a href="/" className="btn btn-ghost">
              🏠 Trang chủ
            </a>
          </li>
          <li>
            <a href="/movies" className="btn btn-ghost">
              🎬 Phim lẻ
            </a>
          </li>
          <li>
            <a href="/series" className="btn btn-ghost">
              📺 Phim bộ
            </a>
          </li>
          <li>
            <details>
              <summary className="btn btn-ghost">🎭 Thể loại</summary>
              <ul className="menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li>
                  <a href="/genre/action">Hành động</a>
                </li>
                <li>
                  <a href="/genre/comedy">Hài kịch</a>
                </li>
                <li>
                  <a href="/genre/drama">Chính kịch</a>
                </li>
                <li>
                  <a href="/genre/horror">Kinh dị</a>
                </li>
                <li>
                  <a href="/genre/romance">Lãng mạn</a>
                </li>
                <li>
                  <a href="/genre/sci-fi">Khoa học viễn tưởng</a>
                </li>
              </ul>
            </details>
          </li>
        </ul>
      </div>

      {/* Right side - Search, Theme toggle, User menu */}
      <div className="navbar-end gap-2">
        {/* Search */}
        <div className="form-control">
          <form onSubmit={handleSearch} className="input-group">
            <input
              type="text"
              placeholder="Tìm phim..."
              className="input input-bordered input-sm w-full max-w-xs"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button type="submit" className="btn btn-square btn-sm">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </button>
          </form>
        </div>

        {/* Theme toggle */}
        <label className="swap swap-rotate btn btn-ghost btn-circle">
          <input type="checkbox" className="theme-controller" value="dark" />
          {/* Sun icon */}
          <svg
            className="swap-off h-6 w-6 fill-current"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path d="M5.64,17l-.71.71a1,1,0,0,0,0,1.41,1,1,0,0,0,1.41,0l.71-.71A1,1,0,0,0,5.64,17ZM5,12a1,1,0,0,0-1-1H3a1,1,0,0,0,0,2H4A1,1,0,0,0,5,12Zm7-7a1,1,0,0,0,1-1V3a1,1,0,0,0-2,0V4A1,1,0,0,0,12,5ZM5.64,7.05a1,1,0,0,0,.7.29,1,1,0,0,0,.71-.29,1,1,0,0,0,0-1.41l-.71-.71A1,1,0,0,0,4.93,6.34Zm12,.29a1,1,0,0,0,.7-.29l.71-.71a1,1,0,1,0-1.41-1.41L17,5.64a1,1,0,0,0,0,1.41A1,1,0,0,0,17.66,7.34ZM21,11H20a1,1,0,0,0,0,2h1a1,1,0,0,0,0-2Zm-9,8a1,1,0,0,0-1,1v1a1,1,0,0,0,2,0V20A1,1,0,0,0,12,19ZM18.36,17A1,1,0,0,0,17,18.36l.71.71a1,1,0,0,0,1.41,0,1,1,0,0,0,0-1.41ZM12,6.5A5.5,5.5,0,1,0,17.5,12,5.51,5.51,0,0,0,12,6.5Zm0,9A3.5,3.5,0,1,1,15.5,12,3.5,3.5,0,0,1,12,15.5Z" />
          </svg>
          {/* Moon icon */}
          <svg
            className="swap-on h-6 w-6 fill-current"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path d="M21.64,13a1,1,0,0,0-1.05-.14,8.05,8.05,0,0,1-3.37.73A8.15,8.15,0,0,1,9.08,5.49a8.59,8.59,0,0,1,.25-2A1,1,0,0,0,8,2.36,10.14,10.14,0,1,0,22,14.05,1,1,0,0,0,21.64,13Zm-9.5,6.69A8.14,8.14,0,0,1,7.08,5.22v.27A10.15,10.15,0,0,0,17.22,15.63a9.79,9.79,0,0,0,2.1-.22A8.11,8.11,0,0,1,12.14,19.73Z" />
          </svg>
        </label>

        {/* User menu */}
        <div className="dropdown dropdown-end">
          <div
            tabIndex={0}
            role="button"
            className="btn btn-ghost btn-circle avatar"
          >
            <div className="w-8 rounded-full">
              <div className="avatar placeholder">
                <div className="bg-neutral text-neutral-content w-8 rounded-full">
                  <span className="text-xs">👤</span>
                </div>
              </div>
            </div>
          </div>
          <ul
            tabIndex={0}
            className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow"
          >
            <li>
              <a className="justify-between">
                Hồ sơ
                <span className="badge">Mới</span>
              </a>
            </li>
            <li>
              <a>Danh sách yêu thích</a>
            </li>
            <li>
              <a>Lịch sử xem</a>
            </li>
            <li>
              <a>Cài đặt</a>
            </li>
            <li>
              <a>Đăng xuất</a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AppBar;
