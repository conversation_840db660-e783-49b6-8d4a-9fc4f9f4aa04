"use client";

import React from "react";

const AppBar = () => {
  return (
    <div className="navbar bg-base-100 shadow-lg sticky top-0 z-50">
      {/* Mobile menu button */}
      <div className="navbar-start">
        <div className="dropdown">
          <div tabIndex={0} role="button" className="btn btn-ghost">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 6h16M4 12h8m-8 6h16"
              />
            </svg>
          </div>
          <ul
            tabIndex={0}
            className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-64 p-2 shadow-xl border"
          >
            <li>
              <a href="/" className="text-base py-3">
                <span className="text-xl">🏠</span>
                Trang chủ
              </a>
            </li>
            <li>
              <a href="/movies" className="text-base py-3">
                <span className="text-xl">🎬</span>
                Phim lẻ
              </a>
            </li>
            <li>
              <a href="/series" className="text-base py-3">
                <span className="text-xl">📺</span>
                Phim bộ
              </a>
            </li>
            <li>
              <details>
                <summary className="text-base py-3">
                  <span className="text-xl">🎭</span>
                  Thể loại
                </summary>
                <ul className="p-2 bg-base-200 rounded-lg mt-2">
                  <li>
                    <a href="/genre/action" className="py-2">
                      Hành động
                    </a>
                  </li>
                  <li>
                    <a href="/genre/comedy" className="py-2">
                      Hài kịch
                    </a>
                  </li>
                  <li>
                    <a href="/genre/drama" className="py-2">
                      Chính kịch
                    </a>
                  </li>
                  <li>
                    <a href="/genre/horror" className="py-2">
                      Kinh dị
                    </a>
                  </li>
                  <li>
                    <a href="/genre/romance" className="py-2">
                      Lãng mạn
                    </a>
                  </li>
                  <li>
                    <a href="/genre/sci-fi" className="py-2">
                      Khoa học viễn tưởng
                    </a>
                  </li>
                </ul>
              </details>
            </li>
            <div className="divider my-2"></div>
            <li>
              <a href="/favorites" className="text-base py-3">
                <span className="text-xl">❤️</span>
                Yêu thích
              </a>
            </li>
            <li>
              <a href="/history" className="text-base py-3">
                <span className="text-xl">📖</span>
                Lịch sử xem
              </a>
            </li>
          </ul>
        </div>
      </div>

      {/* Logo/Brand - Center */}
      <div className="navbar-center">
        <a href="/" className="btn btn-ghost text-xl font-bold">
          <span className="text-3xl">🎬</span>
          <span className="text-primary font-extrabold">AZ Movie</span>
        </a>
      </div>

      {/* Right side - Theme toggle and User menu */}
      <div className="navbar-end">
        {/* Theme toggle */}
        <label className="swap swap-rotate btn btn-ghost btn-circle">
          <input type="checkbox" className="theme-controller" value="dark" />
          {/* Sun icon */}
          <svg
            className="swap-off h-6 w-6 fill-current"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path d="M5.64,17l-.71.71a1,1,0,0,0,0,1.41,1,1,0,0,0,1.41,0l.71-.71A1,1,0,0,0,5.64,17ZM5,12a1,1,0,0,0-1-1H3a1,1,0,0,0,0,2H4A1,1,0,0,0,5,12Zm7-7a1,1,0,0,0,1-1V3a1,1,0,0,0-2,0V4A1,1,0,0,0,12,5ZM5.64,7.05a1,1,0,0,0,.7.29,1,1,0,0,0,.71-.29,1,1,0,0,0,0-1.41l-.71-.71A1,1,0,0,0,4.93,6.34Zm12,.29a1,1,0,0,0,.7-.29l.71-.71a1,1,0,1,0-1.41-1.41L17,5.64a1,1,0,0,0,0,1.41A1,1,0,0,0,17.66,7.34ZM21,11H20a1,1,0,0,0,0,2h1a1,1,0,0,0,0-2Zm-9,8a1,1,0,0,0-1,1v1a1,1,0,0,0,2,0V20A1,1,0,0,0,12,19ZM18.36,17A1,1,0,0,0,17,18.36l.71.71a1,1,0,0,0,1.41,0,1,1,0,0,0,0-1.41ZM12,6.5A5.5,5.5,0,1,0,17.5,12,5.51,5.51,0,0,0,12,6.5Zm0,9A3.5,3.5,0,1,1,15.5,12,3.5,3.5,0,0,1,12,15.5Z" />
          </svg>
          {/* Moon icon */}
          <svg
            className="swap-on h-6 w-6 fill-current"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
          >
            <path d="M21.64,13a1,1,0,0,0-1.05-.14,8.05,8.05,0,0,1-3.37.73A8.15,8.15,0,0,1,9.08,5.49a8.59,8.59,0,0,1,.25-2A1,1,0,0,0,8,2.36,10.14,10.14,0,1,0,22,14.05,1,1,0,0,0,21.64,13Zm-9.5,6.69A8.14,8.14,0,0,1,7.08,5.22v.27A10.15,10.15,0,0,0,17.22,15.63a9.79,9.79,0,0,0,2.1-.22A8.11,8.11,0,0,1,12.14,19.73Z" />
          </svg>
        </label>

        {/* User menu */}
        <div className="dropdown dropdown-end">
          <div
            tabIndex={0}
            role="button"
            className="btn btn-ghost btn-circle avatar"
          >
            <div className="w-10 rounded-full">
              <div className="avatar placeholder">
                <div className="bg-primary text-primary-content w-10 rounded-full">
                  <span className="text-lg">👤</span>
                </div>
              </div>
            </div>
          </div>
          <ul
            tabIndex={0}
            className="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-64 p-2 shadow-xl border"
          >
            <li>
              <a className="justify-between text-base py-3">
                <span>
                  <span className="text-xl mr-2">👤</span>
                  Hồ sơ
                </span>
                <span className="badge badge-primary">Mới</span>
              </a>
            </li>
            <li>
              <a className="text-base py-3">
                <span className="text-xl mr-2">❤️</span>
                Danh sách yêu thích
              </a>
            </li>
            <li>
              <a className="text-base py-3">
                <span className="text-xl mr-2">📖</span>
                Lịch sử xem
              </a>
            </li>
            <li>
              <a className="text-base py-3">
                <span className="text-xl mr-2">⚙️</span>
                Cài đặt
              </a>
            </li>
            <div className="divider my-2"></div>
            <li>
              <a className="text-base py-3 text-error">
                <span className="text-xl mr-2">🚪</span>
                Đăng xuất
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AppBar;
