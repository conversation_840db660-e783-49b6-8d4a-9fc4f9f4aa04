if(!self.define){let e,s={};const n=(n,i)=>(n=new URL(n+".js",i).href,s[n]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()})).then((()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e})));self.define=(i,a)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let t={};const r=e=>n(e,c),o={module:{uri:c},exports:t,require:r};s[c]=Promise.all(i.map((e=>o[e]||r(e)))).then((e=>(a(...e),t)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"324f68d563f7dba57a1e6815f295b725"},{url:"/_next/static/F3hY0FyhcN_VjD8Kruqky/_buildManifest.js",revision:"b61f254a9426f08591969682b3f0fa78"},{url:"/_next/static/F3hY0FyhcN_VjD8Kruqky/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/341.df3329d77a5faa19.js",revision:"df3329d77a5faa19"},{url:"/_next/static/chunks/472.a3826d29d6854395.js",revision:"a3826d29d6854395"},{url:"/_next/static/chunks/4bd1b696-67ee12fb04071d3b.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/63-31560fccd30440b1.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/684-575ed63869f48d14.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/app/_not-found/page-f08302ee705a96b1.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/app/layout-143c7bf70523a5be.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/app/page-7216f3908e34fcb5.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/framework-f593a28cde54158e.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/main-a91901cbbfaae197.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/main-app-0420004caf4e25e0.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/pages/_app-92f2aae776f86b9c.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/pages/_error-1c75af675ede1ae6.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-6c1dd749d5a07ce9.js",revision:"F3hY0FyhcN_VjD8Kruqky"},{url:"/_next/static/css/deb8e469bf5e7676.css",revision:"deb8e469bf5e7676"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/icons/fire-128.png",revision:"42e54a888c57cdac6195d679bc10d61d"},{url:"/icons/fire-144.png",revision:"a1cd23662eb26ac2972037cc987065c1"},{url:"/icons/fire-152.png",revision:"b3a9528e14b7dbc771b8cc2d256e12b0"},{url:"/icons/fire-16.png",revision:"59f44154a69d94d41b6598e7fdbb6dd5"},{url:"/icons/fire-180.png",revision:"21cb4c907ae40b2b99f04ba1fe8aa708"},{url:"/icons/fire-192.png",revision:"2d7be3df943c1929053a786856b75975"},{url:"/icons/fire-256.png",revision:"9ef9b379c499a20d5d63ccaad324a1d7"},{url:"/icons/fire-48.png",revision:"10528e00c9c1595ab6aebf3cdb882e42"},{url:"/icons/fire-512.png",revision:"ce79f22941d6b3a1d3e3ad38784527d1"},{url:"/icons/fire-64.png",revision:"c1efcd5d78cf8b2e31c10e372e1ad5ff"},{url:"/icons/fire-72.png",revision:"50b38282bf6fc0880981f0fb9e3b76f1"},{url:"/icons/fire-96.png",revision:"1b05d86ee2b3e4f870eada6e1d26c17b"},{url:"/manifest.json",revision:"77a327643cebd0f4ac9da005dc39788b"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:n,state:i})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
